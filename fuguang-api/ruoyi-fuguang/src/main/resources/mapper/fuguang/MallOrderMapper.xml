<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.MallOrderMapper">

    <resultMap type="MallOrder" id="MallOrderResult">
        <result property="orderId"          column="order_id"          />
        <result property="orderNo"          column="order_no"          />
        <result property="userId"           column="user_id"           />
        <result property="totalAmount"      column="total_amount"      />
        <result property="payAmount"        column="pay_amount"        />
        <result property="orderStatus"      column="order_status"      />
        <result property="payStatus"        column="pay_status"        />
        <result property="payType"          column="pay_type"          />
        <result property="payTime"          column="pay_time"          />
        <result property="deliveryTime"     column="delivery_time"     />
        <result property="finishTime"       column="finish_time"       />
        <result property="cancelTime"       column="cancel_time"       />
        <result property="receiverName"     column="receiver_name"     />
        <result property="receiverPhone"    column="receiver_phone"    />
        <result property="receiverAddress"  column="receiver_address"  />
        <result property="deliveryFee"      column="delivery_fee"      />
        <result property="remark"           column="remark"            />
        <result property="createTime"       column="create_time"       />
        <result property="updateTime"       column="update_time"       />
    </resultMap>

    <sql id="selectMallOrderVo">
        select order_id, order_no, user_id, total_amount, pay_amount, order_status, pay_status, pay_type,
               pay_time, delivery_time, finish_time, cancel_time, receiver_name, receiver_phone,
               receiver_address, delivery_fee, remark, create_time, update_time
        from mall_order
    </sql>

    <select id="selectMallOrderList" parameterType="MallOrder" resultMap="MallOrderResult">
        <include refid="selectMallOrderVo"/>
        <where>
            <if test="orderNo != null  and orderNo != ''"> and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
            <if test="payStatus != null  and payStatus != ''"> and pay_status = #{payStatus}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="receiverName != null  and receiverName != ''"> and receiver_name like concat('%', #{receiverName}, '%')</if>
            <if test="receiverPhone != null  and receiverPhone != ''"> and receiver_phone like concat('%', #{receiverPhone}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectMallOrderByOrderId" parameterType="Long" resultMap="MallOrderResult">
        <include refid="selectMallOrderVo"/>
        where order_id = #{orderId}
    </select>

    <select id="selectMallOrderByOrderNo" parameterType="String" resultMap="MallOrderResult">
        <include refid="selectMallOrderVo"/>
        where order_no = #{orderNo}
    </select>

    <select id="selectOrderListByUserId" resultMap="MallOrderResult">
        <include refid="selectMallOrderVo"/>
        where user_id = #{userId}
        <if test="orderStatus != null and orderStatus != ''">
            and order_status = #{orderStatus}
        </if>
        order by create_time desc
    </select>

    <select id="selectPendingPaymentCount" parameterType="Long" resultType="int">
        select count(*) from mall_order where user_id = #{userId} and order_status = '0'
    </select>

    <select id="selectPendingDeliveryCount" parameterType="Long" resultType="int">
        select count(*) from mall_order where user_id = #{userId} and order_status = '1'
    </select>

    <select id="selectPendingReceiveCount" parameterType="Long" resultType="int">
        select count(*) from mall_order where user_id = #{userId} and order_status = '2'
    </select>

    <select id="countOrdersByUserIdAndStatus" resultType="int">
        select count(*) from mall_order
        where user_id = #{userId} and order_status = #{orderStatus}
    </select>

    <insert id="insertMallOrder" parameterType="MallOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into mall_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="deliveryTime != null">delivery_time,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="cancelTime != null">cancel_time,</if>
            <if test="receiverName != null">receiver_name,</if>
            <if test="receiverPhone != null">receiver_phone,</if>
            <if test="receiverAddress != null">receiver_address,</if>
            <if test="deliveryFee != null">delivery_fee,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="deliveryTime != null">#{deliveryTime},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="cancelTime != null">#{cancelTime},</if>
            <if test="receiverName != null">#{receiverName},</if>
            <if test="receiverPhone != null">#{receiverPhone},</if>
            <if test="receiverAddress != null">#{receiverAddress},</if>
            <if test="deliveryFee != null">#{deliveryFee},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMallOrder" parameterType="MallOrder">
        update mall_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="deliveryTime != null">delivery_time = #{deliveryTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="receiverName != null">receiver_name = #{receiverName},</if>
            <if test="receiverPhone != null">receiver_phone = #{receiverPhone},</if>
            <if test="receiverAddress != null">receiver_address = #{receiverAddress},</if>
            <if test="deliveryFee != null">delivery_fee = #{deliveryFee},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where order_id = #{orderId}
    </update>

    <update id="updateOrderStatus">
        update mall_order set order_status = #{orderStatus} where order_id = #{orderId}
    </update>

    <update id="updatePayStatus">
        update mall_order set pay_status = #{payStatus} where order_id = #{orderId}
    </update>

    <update id="updatePayStatusByOrderNo">
        update mall_order set pay_status = #{payStatus} where order_no = #{orderNo}
    </update>

    <delete id="deleteMallOrderByOrderId" parameterType="Long">
        delete from mall_order where order_id = #{orderId}
    </delete>

    <delete id="deleteMallOrderByOrderIds" parameterType="String">
        delete from mall_order where order_id in
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

</mapper>